// Google OAuth Configuration
// This file contains the configuration for Google Sign-In

const GOOGLE_AUTH_CONFIG = {
    // Replace this with your actual Google OAuth Client ID
    // You can get this from the Google Cloud Console:
    // 1. Go to https://console.cloud.google.com/
    // 2. Create a new project or select existing one
    // 3. Enable Google+ API
    // 4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
    // 5. Set authorized redirect URIs to include your domain + /auth-callback.html
    clientId: '************-8h8qj9v2q1q1q1q1q1q1q1q1q1q1q1q1.apps.googleusercontent.com',
    
    // Scopes to request from Google
    scopes: [
        'openid',
        'email', 
        'profile'
    ],
    
    // Redirect URI (must match what's configured in Google Cloud Console)
    redirectUri: 'https://barberbrotherslegacy.com/auth-callback.html',
    
    // Response type for OAuth flow
    responseType: 'code',
    
    // Access type
    accessType: 'offline',
    
    // Prompt parameter
    prompt: 'select_account'
};

// Function to get the Google OAuth URL
function getGoogleAuthUrl(source) {
    const state = encodeURIComponent(JSON.stringify({ 
        source: source, 
        timestamp: Date.now() 
    }));
    
    const params = new URLSearchParams({
        client_id: GOOGLE_AUTH_CONFIG.clientId,
        redirect_uri: GOOGLE_AUTH_CONFIG.redirectUri,
        scope: GOOGLE_AUTH_CONFIG.scopes.join(' '),
        response_type: GOOGLE_AUTH_CONFIG.responseType,
        state: state,
        access_type: GOOGLE_AUTH_CONFIG.accessType,
        prompt: GOOGLE_AUTH_CONFIG.prompt
    });
    
    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GOOGLE_AUTH_CONFIG, getGoogleAuthUrl };
} else {
    window.GOOGLE_AUTH_CONFIG = GOOGLE_AUTH_CONFIG;
    window.getGoogleAuthUrl = getGoogleAuthUrl;
}
