/* Global Styles */
:root {
    --primary-color: #D90000; /* Deep Red */
    --secondary-color: #FF3333; /* Lighter Red */
    --dark-color: #000000; /* Black */
    --light-color: #FFFFFF; /* White */
    --gray-color: #121212; /* Very Dark Gray for backgrounds */
    --dark-gray: #1a1a1a; /* Slightly lighter dark gray */
    --text-gray: #FFFFFF; /* White for better readability */
    --card-bg: #1a1a1a; /* Card background */
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-gray);
    line-height: 1.6;
    background-color: var(--gray-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON>', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--light-color);
}

.btn-danger {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(217, 0, 0, 0.3);
}

.btn-danger:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.5);
}

.section-title {
    font-family: 'Oswald', sans-serif;
    font-weight: 700;
    font-size: 36px;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    color: var(--light-color);
    display: flex;
    justify-content: center;
    align-items: center;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    width: 60px;
    height: 3px;
    background-color: #dc3545;
    left: 50%;
    transform: translateX(-50%);
}

.text-center.section-title::after {
    margin-left: auto;
    margin-right: auto;
}

/* Navigation */
.navbar {
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-family: 'Oswald', sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--light-color);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--light-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    transition: color 0.3s;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    background-color: #000;
    display: flex;
    align-items: center;
    color: var(--light-color);
    text-align: center;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7));
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-family: 'Oswald', sans-serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: 3px;
    color: #fff;
    text-transform: uppercase;
}

.hero h2 {
    font-family: 'Oswald', sans-serif;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
    color: #fff;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #fff;
}

.hero .btn-danger {
    padding: 12px 30px;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color: #D90000;
    border: none;
    box-shadow: 0 4px 15px rgba(217, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero .btn-danger:hover {
    transform: translateY(-2px);
    background-color: #FF0000;
    box-shadow: 0 6px 20px rgba(217, 0, 0, 0.4);
}

/* About Section */
.about-img-container {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--primary-color);
}

.about-img {
    width: 100%;
    height: 100%;
    background: url('../images/about.jpeg') center/cover no-repeat;
    transition: transform 0.3s ease;
}

.about-img:hover {
    transform: scale(1.05);
}

/* Add a red glow effect to match the neon theme */
.about-img-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: 0 0 20px rgba(217, 0, 0, 0.3);
    border-radius: 10px;
    pointer-events: none;
}

/* Services Section */
.service-card {
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(217, 0, 0, 0.2);
}

.service-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: 50%;
    margin-bottom: 20px;
    font-size: 2rem;
}

.service-card h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.service-card .price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-top: 15px;
}

.service-card p {
    color: var(--text-gray);
}

/* Gallery Section */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    /* Ensure consistent aspect ratio on desktop */
    aspect-ratio: 4/5;
    min-height: 300px;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top; /* Focus on the top portion where faces usually are */
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Lazy loading styles */
.gallery-lazy-image {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.gallery-lazy-image.loaded {
    opacity: 1;
}

.gallery-lazy-image.loading {
    opacity: 0.7;
    filter: blur(2px);
}

/* Placeholder styling for better UX */
.gallery-item img[src*="data:image/svg"] {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
    100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-placeholder {
    width: 100%;
    height: 100%;
    background: url('../images/gallery-placeholder.jpg') center/cover no-repeat;
    border-radius: 8px;
}

/* My Work Carousel Section */
.work-carousel-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem 0;
}

.work-carousel-card {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-width: 600px;
    width: 100%;
    border: 2px solid var(--primary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.work-carousel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(220, 53, 69, 0.2);
}

.carousel-image-container {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.8s ease-in-out, transform 0.8s ease;
}

.carousel-image.fade-out {
    opacity: 0;
    transform: scale(1.05);
}

.carousel-image.fade-in {
    opacity: 1;
    transform: scale(1);
}

.carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.1) 0%,
        rgba(0, 0, 0, 0) 30%,
        rgba(0, 0, 0, 0) 70%,
        rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 1rem;
}

.carousel-counter {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.carousel-caption {
    padding: 1.5rem;
    text-align: center;
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    color: white;
}

.carousel-caption h5 {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.carousel-caption p {
    color: #ccc;
    margin: 0;
    font-size: 0.95rem;
}

/* Responsive adjustments for carousel */
@media (max-width: 768px) {
    .carousel-image-container {
        height: 300px;
    }

    .work-carousel-card {
        margin: 0 1rem;
    }

    .carousel-caption {
        padding: 1rem;
    }

    .carousel-caption h5 {
        font-size: 1.1rem;
    }

    .carousel-caption p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .carousel-image-container {
        height: 250px;
    }

    .carousel-counter {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Testimonials Section */
.testimonial-card {
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.testimonial-content {
    color: var(--text-gray);
    margin-bottom: 20px;
    font-style: italic;
}

.testimonial-info h4 {
    color: var(--text-gray);
    margin-bottom: 5px;
}

.stars {
    color: var(--primary-color);
}

/* Contact Section */
.contact-info {
    padding: 30px;
    background-color: var(--card-bg);
    color: var(--text-gray);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.contact-info i {
    color: var(--primary-color);
    margin-right: 10px;
}

/* Social Icons */
.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: 50%;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.social-icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.social-icon.facebook {
    background-color: #1877f2;
}

.social-icon.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-icon.youtube {
    background-color: #FF0000;
}

.social-icon.youtube:hover {
    background-color: #FF3333;
}

.social-icon::after {
    content: attr(aria-label);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-icon:hover::after {
    opacity: 1;
}

.contact-form {
    padding: 30px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    height: 100%;
    color: var(--text-gray);
}

/* Form controls */
.form-control {
    background-color: var(--dark-gray);
    border: 1px solid #333;
    color: var(--text-gray);
}

.form-control:focus {
    background-color: var(--dark-gray);
    border-color: var(--primary-color);
    color: var(--text-gray);
    box-shadow: 0 0 0 0.25rem rgba(217, 0, 0, 0.25);
}

.form-control::placeholder {
    color: #999;
}

/* Map Section */
.map-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 30px 0;
}

footer h3 {
    color: var(--light-color);
    margin-bottom: 10px;
}

footer .fa-heart {
    color: var(--primary-color);
}

/* Media Queries */
@media (max-width: 1200px) {
    .hero h1 {
        font-size: 4rem;
    }
    
    .hero h2 {
        font-size: 2.2rem;
    }

    .service-card {
        padding: 25px 15px;
    }
}

@media (max-width: 992px) {
    .hero h1 {
        font-size: 3.5rem;
    }
    
    .hero h2 {
        font-size: 2rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .about-img-container {
        height: 400px;
    }

    .video-wrapper {
        padding-top: 75%; /* Adjust aspect ratio for tablets */
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 500px;
        padding-top: 100px;
    }
    
    .hero h1 {
        font-size: 2.8rem;
    }
    
    .hero h2 {
        font-size: 1.6rem;
    }
    
    .section-title {
        font-size: 2rem;
    }

    .navbar-logo {
        height: 50px;
        width: 50px;
    }
    
    .gallery-item {
        /* Remove fixed height on tablets, use aspect ratio instead */
        aspect-ratio: 3/4;
        min-height: 280px;
        max-height: 350px;
    }

    .gallery-item img {
        /* Ensure better centering on tablet */
        object-position: center 20%;
    }

    .contact-info, .contact-form {
        margin-bottom: 30px;
        padding: 20px;
    }

    .social-icons {
        justify-content: center;
    }

    .footer-logo {
        height: 60px;
        width: 60px;
    }

    /* Improve form elements on mobile */
    .form-control, select.form-control {
        height: 50px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    textarea.form-control {
        height: auto;
    }

    .rotating-gallery {
        height: 250px;
        max-width: 95vw;
    }

    .gallery-arrow {
        width: 36px;
        height: 36px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero {
        min-height: 400px;
        padding-top: 80px;
    }

    .hero h1 {
        font-size: 2.2rem;
    }
    
    .hero h2 {
        font-size: 1.3rem;
    }

    .hero .btn-danger {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .gallery-item {
        /* Remove fixed height on mobile, use flexible aspect ratio */
        aspect-ratio: 3/4;
        min-height: 250px;
        max-height: 320px;
        margin-bottom: 20px;
    }

    .gallery-item img {
        /* Better positioning for mobile - focus on face area */
        object-position: center 15%;
        border-radius: 10px;
    }

    .video-wrapper {
        padding-top: 100%; /* Square aspect ratio for mobile */
    }

    .service-card {
        margin-bottom: 20px;
    }

    .testimonial-card {
        padding: 15px;
    }

    /* Adjust spacing for mobile */
    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    /* Make buttons more touch-friendly */
    .btn {
        padding: 12px 20px;
        min-height: 44px; /* Minimum touch target size */
    }

    .rotating-gallery {
        height: 180px;
    }

    .gallery-arrow {
        width: 28px;
        height: 28px;
        font-size: 1.1rem;
    }
}

/* Handle very small devices */
@media (max-width: 360px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero h2 {
        font-size: 1.1rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }
}

/* Handle landscape orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero h2 {
        font-size: 1.5rem;
    }
}

/* Improve touch targets on mobile */
@media (hover: none) {
    .nav-link, 
    .btn,
    .social-icon {
        cursor: default;
    }

    .video-player::-webkit-media-controls {
        transform: scale(1.2);
        transform-origin: center bottom;
    }
}

/* Fix iOS form elements */
@supports (-webkit-touch-callout: none) {
    input, select, textarea {
        font-size: 16px !important;
    }

    .form-control {
        -webkit-appearance: none;
        border-radius: 8px;
    }
}

/* Navbar Logo */
.navbar-logo {
    height: 60px;
    width: 60px;
    object-fit: contain;
    border-radius: 0; /* Remove border radius for the clock logo */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* Adjust navbar padding to accommodate larger logo */
.navbar {
    padding: 0.8rem 1rem;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Footer Logo */
.footer-logo {
    height: 80px;
    width: 80px;
    object-fit: contain;
    border-radius: 0; /* Remove border radius for the clock logo */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.footer-logo:hover {
    transform: scale(1.05);
}

/* Override any Bootstrap text colors */
.text-muted {
    color: #999 !important;
}

.lead {
    color: var(--text-gray);
}

p {
    color: var(--text-gray);
}

/* Section backgrounds */
section {
    background-color: var(--gray-color);
}

section.bg-light {
    background-color: var(--dark-gray) !important;
}

/* Add subtle borders to separate sections */
section:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Video Gallery Section */
.video-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(217, 0, 0, 0.2);
}

.video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    background-color: #000;
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: none;
}

.video-info {
    padding: 1.5rem;
}

.video-info h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-gray);
}

.video-info p {
    color: var(--text-gray);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Custom video player controls */
.video-player::-webkit-media-controls {
    background-color: rgba(0, 0, 0, 0.7);
}

.video-player::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.7);
}

.video-player::-webkit-media-controls-play-button {
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* Add video section to navigation */
.navbar-nav .nav-link[href="#videos"] {
    position: relative;
}

.navbar-nav .nav-link[href="#videos"]::after {
    content: 'NEW';
    position: absolute;
    top: -8px;
    right: -20px;
    font-size: 0.6rem;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Rotating Gallery Styles */
.rotating-gallery {
    position: relative;
    width: 100%;
    max-width: 600px;
    height: 400px;
    margin: 0 auto 2rem auto;
    perspective: 1000px;
    background: var(--card-bg, #181818);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(217, 0, 0, 0.3);
    overflow: hidden;
}

.rotating-gallery-card {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    overflow: hidden;
    background-color: var(--card-bg, #181818);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.gallery-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    border-radius: 15px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    z-index: 1;
}

.gallery-image.active {
    opacity: 1;
    z-index: 10;
}

.gallery-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(217, 0, 0, 0.85);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    font-size: 2rem;
    cursor: pointer;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: background 0.2s, transform 0.2s;
    outline: none;
}
.gallery-arrow.left { left: 16px; }
.gallery-arrow.right { right: 16px; }
.gallery-arrow:hover, .gallery-arrow:focus {
    background: #b30000;
    transform: translateY(-50%) scale(1.08);
}

@media (max-width: 480px) {
    .rotating-gallery {
        height: 180px;
    }
    .gallery-arrow {
        width: 28px;
        height: 28px;
        font-size: 1.1rem;
    }
}

/* Extra small mobile devices (320px and below) */
@media (max-width: 320px) {
    .gallery-item {
        aspect-ratio: 1/1; /* Square aspect ratio for very small screens */
        min-height: 200px;
        max-height: 250px;
    }

    .gallery-item img {
        object-position: center 10%; /* Focus even more on the top for tiny screens */
    }
}

/* Alert Messages Styling */
.alert {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert h4 {
    margin-bottom: 15px;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.alert small {
    opacity: 0.9;
}

.alert i {
    margin-right: 5px;
}
