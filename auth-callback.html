<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signing you in... - Barber Brothers Legacy</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
            border-color: #dc3545;
            border-right-color: transparent;
        }
        .logo {
            width: 100px;
            height: 100px;
            object-fit: contain;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        .error-message {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .success-message {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <img src="images/time.jpeg" alt="Barber Brothers Legacy" class="logo">
        <h2>Barber Brothers Legacy</h2>
        
        <div id="loading-state">
            <div class="spinner-border text-danger mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Signing you in...</p>
            <p class="text-muted">Please wait while we complete your authentication.</p>
        </div>
        
        <div id="error-state" class="d-none">
            <div class="error-message">
                <h4>Authentication Error</h4>
                <p id="error-message">Something went wrong during sign-in.</p>
                <button class="btn btn-danger mt-2" onclick="goHome()">Return to Home</button>
            </div>
        </div>
        
        <div id="success-state" class="d-none">
            <div class="success-message">
                <h4>✓ Successfully Signed In!</h4>
                <p>Welcome! Redirecting you back to the site...</p>
            </div>
        </div>
    </div>

    <script>
        // Parse URL parameters
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                code: urlParams.get('code'),
                state: urlParams.get('state'),
                error: urlParams.get('error'),
                error_description: urlParams.get('error_description')
            };
        }

        // Show error state
        function showError(message) {
            document.getElementById('loading-state').classList.add('d-none');
            document.getElementById('error-state').classList.remove('d-none');
            document.getElementById('error-message').textContent = message;
        }

        // Show success state
        function showSuccess() {
            document.getElementById('loading-state').classList.add('d-none');
            document.getElementById('success-state').classList.remove('d-none');
        }

        // Go back to home page
        function goHome() {
            window.location.href = '/';
        }

        // Exchange authorization code for user information
        async function exchangeCodeForUserInfo(code) {
            try {
                console.log('🔄 Exchanging authorization code for user info...');
                console.log('📍 Redirect URI:', window.location.origin + '/auth-callback.html');

                // For frontend-only OAuth, we'll use the Google API to get user info
                // First, we need to exchange the code for an access token
                const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        client_id: '424859813197-l2rm1q7h3f1pcifseak1fsblvja7b30f.apps.googleusercontent.com',
                        client_secret: '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',
                        code: code,
                        grant_type: 'authorization_code',
                        redirect_uri: window.location.origin + '/auth-callback.html'
                    })
                });

                console.log('📡 Token response status:', tokenResponse.status);

                if (!tokenResponse.ok) {
                    throw new Error('Failed to exchange code for token');
                }

                const tokenData = await tokenResponse.json();

                // Use the access token to get user info
                const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                    headers: {
                        'Authorization': `Bearer ${tokenData.access_token}`
                    }
                });

                if (!userResponse.ok) {
                    throw new Error('Failed to get user info');
                }

                const userInfo = await userResponse.json();
                return {
                    id: userInfo.id,
                    email: userInfo.email,
                    name: userInfo.name,
                    picture: userInfo.picture,
                    accessToken: tokenData.access_token
                };
            } catch (error) {
                console.error('Error exchanging code for user info:', error);
                // Return basic info if the exchange fails
                return {
                    id: 'unknown',
                    email: '<EMAIL>',
                    name: 'Google User',
                    picture: 'images/time.jpeg'
                };
            }
        }

        // Handle the authentication callback
        async function handleAuthCallback() {
            try {
                console.log('🔐 Starting OAuth callback handling...');
                console.log('📱 User Agent:', navigator.userAgent);
                console.log('🌐 Current URL:', window.location.href);

                const params = getUrlParams();
                console.log('📋 URL Parameters:', params);

                // Check for errors
                if (params.error) {
                    let errorMessage = 'Authentication failed.';
                    console.error('❌ OAuth Error:', params.error);

                    if (params.error === 'access_denied') {
                        errorMessage = 'You cancelled the sign-in process.';
                    } else if (params.error === 'redirect_uri_mismatch') {
                        errorMessage = 'Redirect URI mismatch. Please check your Google Cloud Console configuration.';
                    } else if (params.error === 'invalid_client') {
                        errorMessage = 'Invalid client configuration. Please check your Client ID.';
                    } else if (params.error_description) {
                        errorMessage = params.error_description;
                    }

                    console.error('❌ Error details:', {
                        error: params.error,
                        description: params.error_description,
                        uri: params.error_uri
                    });

                    showError(errorMessage);
                    return;
                }

                // Check for authorization code
                if (!params.code) {
                    showError('No authorization code received from Google.');
                    return;
                }

                // Parse state parameter
                let state = {};
                try {
                    state = JSON.parse(decodeURIComponent(params.state));
                } catch (e) {
                    console.warn('Could not parse state parameter:', e);
                }

                // Exchange authorization code for user info
                const userInfo = await exchangeCodeForUserInfo(params.code);

                // Store authentication data
                const authData = {
                    code: params.code,
                    state: state,
                    timestamp: Date.now(),
                    source: state.source || 'unknown',
                    userInfo: userInfo
                };

                // Store in localStorage for the main site to use
                localStorage.setItem('googleAuthData', JSON.stringify(authData));
                localStorage.setItem('authSuccess', 'true');
                localStorage.setItem('userProfile', JSON.stringify(userInfo));

                // Show success message
                showSuccess();

                // Redirect back to the main site after a short delay
                setTimeout(() => {
                    // Determine where to redirect based on the source
                    let redirectUrl = '/';
                    if (state.source === 'adult' || state.source === 'kids' || state.source === 'full') {
                        redirectUrl = '/#services';
                    } else if (state.source === 'social') {
                        redirectUrl = '/#social-feed';
                    } else if (state.source === 'media') {
                        redirectUrl = '/#media';
                    }
                    
                    window.location.href = redirectUrl;
                }, 2000);

            } catch (error) {
                console.error('Error handling auth callback:', error);
                showError('An unexpected error occurred. Please try again.');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Add a small delay to show the loading state
            setTimeout(handleAuthCallback, 500);
        });
    </script>
</body>
</html>
