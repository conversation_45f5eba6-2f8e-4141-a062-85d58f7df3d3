# Google OAuth Setup Guide

To get the Google Sign-In working properly, you need to set up Google OAuth credentials. Follow these steps:

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Make sure the project is selected in the top dropdown

## Step 2: Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for and enable the following APIs:
   - **Google+ API** (for basic profile information)
   - **People API** (for profile data)

## Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen first:
   - Choose "External" user type
   - Fill in the required fields (App name, User support email, Developer contact)
   - Add your domain to authorized domains
   - Save and continue through the scopes and test users sections

4. For the OAuth 2.0 Client ID:
   - Application type: **Web application**
   - Name: "Barber Brothers Legacy Website"
   - Authorized JavaScript origins:
     - `http://localhost:3000` (for local development)
     - `https://yourdomain.com` (your production domain)
   - Authorized redirect URIs:
     - `http://localhost:3000/auth-callback.html` (for local development)
     - `https://yourdomain.com/auth-callback.html` (your production domain)

5. Click "Create"
6. Copy the Client ID that's generated

## Step 4: Update Your Configuration

1. Open `google-auth-config.js`
2. Replace the `clientId` value with your actual Client ID:
   ```javascript
   clientId: 'YOUR_ACTUAL_CLIENT_ID_HERE.apps.googleusercontent.com'
   ```

3. Update the meta tag in `index.html`:
   ```html
   <meta name="google-signin-client_id" content="YOUR_ACTUAL_CLIENT_ID_HERE.apps.googleusercontent.com">
   ```

## Step 5: Test the Integration

1. Open your website
2. Click on any "Sign in with Google" button
3. You should be redirected to Google's sign-in page
4. After signing in, you should be redirected back to your site

## Important Notes

- **Domain Verification**: Make sure your domain is added to the authorized domains in the OAuth consent screen
- **HTTPS Required**: For production, Google requires HTTPS for OAuth redirects
- **Localhost Exception**: `localhost` and `127.0.0.1` are allowed for development without HTTPS
- **Redirect URI Match**: The redirect URI in your code must exactly match what's configured in Google Cloud Console

## Troubleshooting

### "redirect_uri_mismatch" Error
- Check that your redirect URI exactly matches what's configured in Google Cloud Console
- Make sure you're using the correct protocol (http vs https)
- Verify the domain and path are correct

### "invalid_client" Error
- Double-check your Client ID is correct
- Make sure the Client ID is for a web application, not a mobile app

### "access_blocked" Error
- Your OAuth consent screen might need to be verified by Google
- For testing, add your email to the test users list in the OAuth consent screen

## Current Configuration

The current setup uses:
- Client ID: `424859813197-l2rm1q7h3f1pcifseak1fsblvja7b30f.apps.googleusercontent.com`
- Redirect URI: `{your-domain}/auth-callback.html`
- Scopes: `openid email profile`

✅ **Configuration Complete!** Your Google OAuth is now properly configured.
